<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Inherited view form of sale order-->
    <record id="view_order_form" model="ir.ui.view">
        <field name="name">sale.order.view.form.inherit.sale.discount.total
        </field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.view_order_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='state']" position="before">
                <button string="Approve" type="object" name="action_approve"
                        invisible="state != 'waiting'" class="oe_highlight"
                        groups="sales_team.group_sale_manager"/>
            </xpath>
            <xpath expr="//group[@name='note_group']" position="replace">
                <group name="note_group" col="6">
                    <group name="discount" colspan="2">
                        <field name="discount_type"
                               groups="sale.group_discount_per_so_line"/>
                        <field name="discount_rate"
                               groups="sale.group_discount_per_so_line"/>
                    </group>
                    <group class="oe_subtotal_footer oe_right" colspan="2"
                           name="sale_total">
                        <field name="amount_untaxed" widget='monetary'
                               options="{'currency_field': 'currency_id'}"/>
                        <field name="amount_discount" widget='monetary'
                               options="{'currency_field': 'currency_id'}"/>
                        <field name="amount_tax" widget='monetary'
                               options="{'currency_field': 'currency_id'}"/>
                        <div class="oe_subtotal_footer_separator oe_inline o_td_label">
                            <label for="amount_total"/>
                            <button name="button_dummy"
                                    invisible="state not in ['draft','sent']"
                                    string="(update)"
                                    type="object"
                                    class="oe_edit_only oe_link"/>
                        </div>
                        <field name="amount_total" nolabel="1"
                               class="oe_subtotal_footer_separator"
                               widget='monetary'
                               options="{'currency_field': 'currency_id'}"/>
                    </group>
                    <group colspan="4">
                        <field colspan="2" name="note" nolabel="1"
                               placeholder="Terms and conditions..."/>
                    </group>
                    <group class="oe_subtotal_footer oe_right" colspan="2"
                           name="sale_total">
                        <field name="tax_totals"
                               widget="account-tax-totals-field" nolabel="1"
                               colspan="2" readonly="1"/>
                        <field name="margin_test" invisible="not margin_test"/>
                    </group>
                    <div class="clearfix"/>
                </group>
            </xpath>
            <!-- Roundoff the discount field -->
            <xpath expr="//field[@name='order_line']/list/field[@name='discount']"
                   position="attributes">
                <attribute name="digits">[16, 2]</attribute>
            </xpath>
            <xpath expr="//page/field[@name='order_line']/list/field[@name='price_subtotal']"
                   position="after">
                <field name="total_discount" force_save="1" invisible="1"/>
            </xpath>
        </field>
    </record>
</odoo>
