<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <!--Add field for selecting products-->
    <record id="view_order_form" model="ir.ui.view">
        <field name="name">sale.order.view.inherit.tree.invoice.or.bill.for.selected.order.lines</field>
        <field name="inherit_id" ref="sale.view_order_form"/>
        <field name="model">sale.order</field>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='order_line']/list/field[@name='product_template_id']" position="before">
                <field name="is_product_select" nolabel="1" invisible=" qty_to_invoice  ==  0"/>
            </xpath>
            <xpath expr="//field[@name='order_line']" position="before">
                <div style="display: flex; flex-direction: row; margin: 10px;" invisible="state in ('draft','sent')">
                    <field name="is_qty_to_invoice" invisible="1"/>
                    <button name="action_select_all" class="btn btn-primary mx-2" string="Select All" type="object"
                            invisible=" is_qty_to_invoice "/>
                    <button name="action_deselect_all" class="oe_highlight" string="Deselect All" type="object"
                            invisible=" is_qty_to_invoice "/>
                </div>
            </xpath>
        </field>
    </record>
</odoo>
