<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <!--Cheque print PDF format-->
    <!--Paper format -->
    <record id="paperformat_print_cheque" model="report.paperformat">
        <field name="name">Print Cheque</field>
        <field name="default" eval="True"/>
        <field name="format">A4</field>
        <field name="orientation">Landscape</field>
        <field name="header_line" eval="False"/>
        <field name="margin_top">1</field>
        <field name="margin_left">1</field>
        <field name="header_spacing" eval="False"/>
        <field name="dpi">90</field>
    </record>
    <!--Cheque print action-->
    <record id="print_cheque_action" model="ir.actions.report">
        <field name="name">Cheque</field>
        <field name="model">cheque.format</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">odoo_print_cheque.cheque_print</field>
        <field name="report_file">odoo_print_cheque.cheque_print</field>
        <field name="paperformat_id"
               ref="odoo_print_cheque.paperformat_print_cheque"/>
        <field name="binding_model_id" ref="model_cheque_format"/>
        <field name="binding_type">report</field>
    </record>
    <!--Test Cheque print PDF format-->
    <!--Paper format -->
    <record id="paperformat_print_test_cheque" model="report.paperformat">
        <field name="name">Print Test Cheque</field>
        <field name="default" eval="True"/>
        <field name="format">A4</field>
        <field name="orientation">Landscape</field>
        <field name="header_line" eval="False"/>
        <field name="margin_top">1</field>
        <field name="margin_left">1</field>
        <field name="header_spacing" eval="False"/>
        <field name="dpi">90</field>
    </record>
    <!--Print test cheque Action-->
    <record id="test_cheque_action" model="ir.actions.report">
        <field name="name">Test Print</field>
        <field name="model">cheque.format</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">odoo_print_cheque.cheque_test_print</field>
        <field name="report_file">odoo_print_cheque.cheque_test_print</field>
        <field name="paperformat_id"
               ref="odoo_print_cheque.paperformat_print_test_cheque"/>
        <field name="binding_model_id" ref="model_cheque_format"/>
        <field name="binding_type">report</field>
    </record>
</odoo>
