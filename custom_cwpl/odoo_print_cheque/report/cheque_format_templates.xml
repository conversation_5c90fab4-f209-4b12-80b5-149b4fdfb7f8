<?xml version="1.0" encoding="utf-8" ?>
<odoo>   <!--Cheque print template-->
    <template id="cheque_print">
        <t t-call="web.html_container">
            <t t-call="web.basic_layout">
                <div t-attf-style="width:{{cheque_width}}px; height:{{cheque_height}}px; font-size:{{font_size}}px;">
                    <!--Account payee cheque format-->
                    <div t-if="is_account_payee">
                        <img src="odoo_print_cheque/static/src/img/accountpay.png"
                            t-attf-style="margin-left:{{a_c_payee_left_margin}}px; margin-top:{{a_c_payee_top_margin}}px; width:{{a_c_payee_width}}px; height:{{a_c_payee_height}}px"/>
                    </div>
                    <!--Cheque sequence number format-->
                    <div t-attf-style="margin-left:{{cheque_no_lm}}px; margin-top:{{cheque_no_tm}}px;">
                        <t t-if="print_cheque_number">
                            <span>
                                <t t-esc="check_number"/>
                            </span>
                        </t>
                        <t t-else=""/>
                    </div>
                    <!--Cheque date format-->
                    <div t-attf-style="margin-left:{{date_left_margin}}px; margin-top:{{date_top_margin}}px; letter-spacing: {{date_letter_spacing}}em; margin-right: 0px;">
                        <t t-esc="cheque_date"/>
                    </div>
                    <!--Partner Name format-->
                    <div t-attf-style="margin-left:{{beneficiary_left_margin}}px; margin-top:{{beneficiary_top_margin}}px;">
                        <t t-esc="partner"/>
                    </div>
                    <!-- Amount in words format-->
                    <div t-attf-style="margin-left:{{amount_word_lm}}px; margin-top:{{amount_word_tm}}px; letter-spacing: {{amount_word_ls}}em; width:{{amount_word_width}}px; line-height: {{amount_word_lh}};">
                        <t t-esc="amount_in_words"/>
                        Only
                    </div>
                    <!--Amount in digits format-->
                    <div t-attf-style="margin-left:{{amount_digit_lm}}px; margin-top:{{amount_digit_tm}}px; letter-spacing: {{amount_digit_ls}}em; font-size:{{amount_digit_size}}px;">
                        <t t-if="print_currency">
                            <t t-esc="currency_symbol"/>
                        </t>
                        <t t-esc="amount_in_digit"/>/-
                    </div>
                </div>
            </t>
        </t>
    </template>
    <!--Test cheque print template-->
    <template id="cheque_test_print">
        <t t-call="web.html_container">
            <t t-call="web.basic_layout">
                <div t-attf-style="border:1px solid black; width:{{cheque_width}}px; height:{{cheque_height}}px; font-size:{{font_size}}px;">
                    <!--Account payee cheque format-->
                    <div t-if="is_account_payee">
                        <img src="odoo_print_cheque/static/src/img/accountpay.png"
                             t-attf-style="margin-left:{{a_c_payee_left_margin}}px; margin-top:{{a_c_payee_top_margin}}px;
                                           width:{{a_c_payee_width}}px; height:{{a_c_payee_height}}px"/>
                    </div>
                    <!--Cheque sequence number format-->
                    <div t-attf-style="margin-left:{{cheque_no_lm}}px; margin-top:{{cheque_no_tm}}px;">
                        <t t-if="print_cheque_number">
                            <span>0000001</span>
                        </t>
                        <t t-else=""/>
                    </div>
                    <!--Cheque date format-->
                    <div t-attf-style="margin-left:{{date_left_margin}}px; margin-top:{{date_top_margin}}px; letter-spacing:{{date_letter_spacing}}em;">
                        <t t-if="date_remove_slashes">
                            <span>30 08 2001</span>
                        </t>
                        <t t-else="">
                            30/08/2001
                        </t>
                    </div>
                    <!--Partner Name format-->
                    <div t-attf-style="margin-left:{{beneficiary_left_margin}}px; margin-top:{{beneficiary_top_margin}}px;">
                        Test Customer
                    </div>
                    <!-- Amount in words format-->
                    <div t-attf-style="margin-left:{{amount_word_lm}}px; margin-top:{{amount_word_tm}}px; letter-spacing:{{amount_word_ls}}em; width:{{amount_word_width}}px; line-height: {{amount_word_lh}};">
                        One Million, Two Hundred And Forty Thousand, Six Hundred And Twelve Dollars
                    </div>
                    <!--Amount in digits format-->
                    <div t-attf-style="margin-left:{{amount_digit_lm}}px; margin-top:{{amount_digit_tm}}px; letter-spacing:{{amount_digit_ls}}em;
                                       font-size:{{amount_digit_size}}px;">
                        <t t-if="print_currency">
                            <t t-esc="currency_symbol"/>
                        </t>
                        12,40,612 /-
                    </div>
                </div>
            </t>
        </t>
    </template>
</odoo>
