<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--    Print cheque by selecting cheque format   -->
    <record id="cheque_types_view_form" model="ir.ui.view">
        <field name="name">cheque.types.view.form</field>
        <field name="model">cheque.types</field>
        <field name="arch" type="xml">
            <form string="Cheque Print wizard">
                <group>
                    <field name="cheque_format_id"/>
                </group>
                <footer>
                    <button string="Print" name="action_print_selected_cheque"
                            type="object"
                            class="oe_highlight" data-hotkey="q"
                            help="Select a format"/>
                    <button string="Cancel" class="btn btn-secondary"
                            special="cancel" help="Cancel Print"/>
                </footer>
            </form>
        </field>
    </record>
</odoo>
